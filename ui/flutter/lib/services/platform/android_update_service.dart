/// LEGALESE:   Copyright (c) 2025, UNISASE Corporation.
///
/// This source code is confidential, proprietary, and contains trade
/// secrets that are the sole property of UNISASE Corporation.
/// Copy and/or distribution of this source code or disassembly or reverse
/// engineering of the resultant object code are strictly forbidden without
/// the written consent of UNISASE Corporation LLC.
///
/// FILE NAME :      android_update_service.dart
///
/// DESCRIPTION :    Android平台特定的更新服务实现
///
/// AUTHOR :         wei
///
/// HISTORY :        04/08/2025 create

import 'dart:io';

import 'package:android_intent_plus/android_intent.dart';
import 'package:crypto/crypto.dart';
import 'package:flutter/foundation.dart';
import 'package:package_info_plus/package_info_plus.dart';
import 'package:path_provider/path_provider.dart';

import 'platform_update_service.dart';

/// AndroidUpdateService
///
/// PURPOSE:
///     Android平台特定的更新服务实现，处理APK安装包的下载和安装
///
/// FEATURES:
///     - APK安装意图调用
///     - 文件完整性验证（SHA-256）
///     - Android安装权限处理
///     - 外部存储目录管理
///     - 网络状态检测
///     - WiFi连接检查
///     - 存储空间检查
///
/// USAGE:
///     AndroidUpdateService service = AndroidUpdateService();
///     await service.installUpdate('/path/to/update.apk');
class AndroidUpdateService implements PlatformUpdateService {
  static const String _platformType = 'android';
  static const String _fileExtension = '.apk';

  @override
  Future<void> installUpdate(String filePath) async {
    if (!await File(filePath).exists()) {
      throw Exception('Update file not found: $filePath');
    }

    try {
      // 使用Android Intent启动APK安装
      final intent = AndroidIntent(
        action: 'android.intent.action.VIEW',
        data: 'file://$filePath',
        type: 'application/vnd.android.package-archive',
        flags: [
          0x10000000, // FLAG_ACTIVITY_NEW_TASK
          0x00000001, // FLAG_GRANT_READ_URI_PERMISSION
        ],
      );

      await intent.launch();
    } catch (e) {
      throw Exception('Failed to start APK installation: $e');
    }
  }

  @override
  Future<void> openAppStore(String appId) async {
    try {
      // 打开Google Play Store
      final intent = AndroidIntent(
        action: 'android.intent.action.VIEW',
        data: 'market://details?id=$appId',
      );

      await intent.launch();
    } catch (e) {
      // 如果Play Store不可用，尝试使用浏览器打开
      try {
        final webIntent = AndroidIntent(
          action: 'android.intent.action.VIEW',
          data: 'https://play.google.com/store/apps/details?id=$appId',
        );
        await webIntent.launch();
      } catch (e2) {
        throw Exception('Failed to open app store: $e2');
      }
    }
  }

  @override
  String getDownloadDirectory() {
    // Android上使用应用的内部存储目录，无需额外权限
    // 注意：这个路径在运行时确定，这里返回一个占位符
    return 'app_internal_storage/updates';
  }

  /// _getActualDownloadDirectory
  ///
  /// DESCRIPTION:
  ///     获取实际的下载目录路径，使用Android内部存储避免权限和只读问题
  ///     按优先级顺序：应用内部文件目录 > 应用缓存目录 > 临时目录
  ///
  /// RETURNS:
  ///     Future<String> - 实际的下载目录路径
  Future<String> _getActualDownloadDirectory() async {
    try {
      // 优先使用应用内部文件目录（/data/data/package/files），完全私有，无需权限
      final directory = await getApplicationDocumentsDirectory();
      final updateDir = '${directory.path}/updates';

      // 测试目录是否可写
      final dir = Directory(updateDir);
      if (!await dir.exists()) {
        await dir.create(recursive: true);
      }

      // 测试写入权限
      final testFile = File('$updateDir/.write_test');
      await testFile.writeAsString('test');
      await testFile.delete();

      debugPrint('Using app documents directory: $updateDir');
      return updateDir;
    } catch (e) {
      debugPrint('App documents directory failed: $e');

      // 如果文档目录不可用，使用应用缓存目录
      try {
        final directory = await getTemporaryDirectory();
        final updateDir = '${directory.path}/updates';

        // 确保目录存在
        final dir = Directory(updateDir);
        if (!await dir.exists()) {
          await dir.create(recursive: true);
        }

        // 测试写入权限
        final testFile = File('$updateDir/.write_test');
        await testFile.writeAsString('test');
        await testFile.delete();

        debugPrint('Using temp directory: $updateDir');
        return updateDir;
      } catch (e2) {
        debugPrint('Temp directory failed: $e2');

        // 最后尝试使用应用内部存储的files目录
        try {
          // 使用Platform Channel获取Android内部存储路径
          final internalDir = await _getAndroidInternalStorageDirectory();
          final updateDir = '$internalDir/updates';

          // 确保目录存在
          final dir = Directory(updateDir);
          if (!await dir.exists()) {
            await dir.create(recursive: true);
          }

          // 测试写入权限
          final testFile = File('$updateDir/.write_test');
          await testFile.writeAsString('test');
          await testFile.delete();

          debugPrint('Using internal storage directory: $updateDir');
          return updateDir;
        } catch (e3) {
          debugPrint('All storage options failed: $e3');
          throw Exception('Unable to find writable storage directory: $e3');
        }
      }
    }
  }

  /// _getAndroidInternalStorageDirectory
  ///
  /// DESCRIPTION:
  ///     获取Android内部存储目录路径，作为最后的回退选项
  ///
  /// RETURNS:
  ///     Future<String> - Android内部存储目录路径
  Future<String> _getAndroidInternalStorageDirectory() async {
    try {
      // 尝试使用path_provider的内部存储方法
      final directory = await getApplicationDocumentsDirectory();
      // 获取父目录，通常是 /data/data/package/files 的父目录
      final parentDir = directory.parent.path;
      return '$parentDir/files';
    } catch (e) {
      // 如果失败，使用硬编码路径作为最后手段
      return '/data/data/com.panabit.client/files';
    }
  }

  @override
  Future<bool> validateFile(String filePath, String expectedHash, String hashType) async {
    try {
      final file = File(filePath);
      if (!await file.exists()) {
        return false;
      }

      // 读取文件内容
      final bytes = await file.readAsBytes();
      
      // 计算哈希值
      String actualHash;
      switch (hashType.toUpperCase()) {
        case 'SHA-256':
        case 'SHA256':
          actualHash = sha256.convert(bytes).toString();
          break;
        case 'MD5':
          actualHash = md5.convert(bytes).toString();
          break;
        default:
          throw Exception('Unsupported hash type: $hashType');
      }

      // 比较哈希值（忽略大小写）
      return actualHash.toLowerCase() == expectedHash.toLowerCase();
    } catch (e) {
      return false;
    }
  }

  @override
  Future<bool> checkPermissions() async {
    // Android上需要检查安装未知来源应用的权限和存储权限
    try {
      // 检查下载目录是否可访问
      final downloadDir = await _getActualDownloadDirectory();
      final directory = Directory(downloadDir);

      // 尝试创建目录来验证权限
      if (!await directory.exists()) {
        await directory.create(recursive: true);
      }

      // 尝试写入测试文件来验证权限
      final testFile = File('${directory.path}/.permission_test');
      await testFile.writeAsString('test');
      await testFile.delete();

      return true;
    } catch (e) {
      debugPrint('Android permission check failed: $e');
      return false;
    }
  }

  @override
  Future<bool> requestPermissions() async {
    // Android上请求安装权限
    try {
      // 首先检查存储权限
      if (!await checkPermissions()) {
        debugPrint('Storage permission check failed');
        return false;
      }

      // 打开设置页面让用户手动开启安装未知来源应用的权限
      const intent = AndroidIntent(
        action: 'android.settings.MANAGE_UNKNOWN_APP_SOURCES',
      );
      await intent.launch();
      return true;
    } catch (e) {
      debugPrint('Android permission request failed: $e');
      return false;
    }
  }

  @override
  bool canInstallUpdates() {
    return true;
  }

  @override
  String getFileExtension() {
    return _fileExtension;
  }

  @override
  Future<void> cleanupOldFiles({int keepLatest = 1}) async {
    try {
      final downloadDirPath = await _getActualDownloadDirectory();
      final downloadDir = Directory(downloadDirPath);

      if (!await downloadDir.exists()) {
        debugPrint('Download directory does not exist: $downloadDirPath');
        return;
      }

      // 获取所有APK文件
      final files = await downloadDir
          .list()
          .where((entity) => entity is File && entity.path.endsWith(_fileExtension))
          .cast<File>()
          .toList();

      if (files.length <= keepLatest) {
        debugPrint('No old files to cleanup, current count: ${files.length}');
        return;
      }

      // 按修改时间排序，保留最新的文件
      files.sort((a, b) => b.lastModifiedSync().compareTo(a.lastModifiedSync()));

      // 删除多余的文件
      for (int i = keepLatest; i < files.length; i++) {
        try {
          await files[i].delete();
          debugPrint('Deleted old update file: ${files[i].path}');
        } catch (e) {
          debugPrint('Failed to delete old update file: ${files[i].path}, error: $e');
        }
      }
    } catch (e) {
      debugPrint('Cleanup old files failed: $e');
    }
  }

  @override
  Future<int> getAvailableSpace() async {
    try {
      final downloadDirPath = await _getActualDownloadDirectory();
      final directory = Directory(downloadDirPath);

      // 确保目录存在
      if (!await directory.exists()) {
        await directory.create(recursive: true);
      }

      // Android上获取可用空间需要调用系统API
      // 这里是简化实现，返回一个估算值
      // 实际应用中可以使用platform channel调用Android API获取真实可用空间
      return 1024 * 1024 * 1024; // 1GB
    } catch (e) {
      return 0;
    }
  }

  @override
  Future<bool> isNetworkAvailable() async {
    try {
      // 尝试连接到一个可靠的服务器来检查网络连接
      final result = await InternetAddress.lookup('google.com');
      return result.isNotEmpty && result[0].rawAddress.isNotEmpty;
    } catch (e) {
      return false;
    }
  }

  @override
  Future<bool> isWifiConnected() async {
    // Android上检查WiFi连接需要调用系统API
    // 这里简化实现，假设有网络连接就检查是否为WiFi
    // 实际实现需要使用connectivity_plus包或调用Android API
    return await isNetworkAvailable();
  }

  @override
  Future<void> showUpdateNotification(String title, String message, {bool isForceUpdate = false}) async {
    // Android上的通知实现需要使用flutter_local_notifications包
    // 这里是简化实现，使用debugPrint避免生产环境输出
    debugPrint('Android Notification: $title - $message');
  }

  @override
  Future<void> hideUpdateNotification() async {
    // 隐藏通知的实现
    debugPrint('Android: Hide notification');
  }

  @override
  String getPlatformType() {
    return _platformType;
  }

  @override
  Future<String> getCurrentVersion() async {
    try {
      final packageInfo = await PackageInfo.fromPlatform();
      return packageInfo.version;
    } catch (e) {
      return '1.0.0'; // 默认版本
    }
  }

  @override
  String? getAppId() {
    // Android平台使用包名作为应用ID
    // 实际实现中应该从配置或PackageInfo获取
    return 'com.example.app'; // 占位符，实际应用中需要配置
  }

  @override
  Future<void> dispose() async {
    // Android平台特定的清理工作
    // 目前没有需要清理的资源
  }

  /// getActualDownloadDirectoryPath
  ///
  /// DESCRIPTION:
  ///     获取实际的下载目录路径，供外部调用
  ///
  /// RETURNS:
  ///     Future<String> - 实际的下载目录路径
  Future<String> getActualDownloadDirectoryPath() async {
    return await _getActualDownloadDirectory();
  }
}
